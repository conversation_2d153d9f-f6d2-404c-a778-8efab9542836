// ============================================================================
// PERSONAL QUEUES EDITING COMPONENT
// ============================================================================

/**
 * Personal Queues Editing Component
 * Handles queue content editing, video management, and reordering
 * Provides interface for modifying saved personal queues
 */

// State management
let currentEditingQueue = null;
let editingQueueVideos = [];
let isEditModalOpen = false;

/**
 * Initialize queue editing component
 */
function initializeQueueEditing() {
  console.log('🎬 Initializing Queue Editing Component...');
  
  // Create edit modal if it doesn't exist
  createEditModal();
  
  // Set up event listeners
  setupEditModalEventListeners();
  
  console.log('✅ Queue Editing Component initialized');
}

/**
 * Open queue editing modal
 * @param {string} queueId - ID of the queue to edit
 */
async function editPersonalQueue(queueId) {
  try {
    console.log('✏️ Opening edit modal for queue:', queueId);
    
    // Check authentication
    const userId = await getCurrentUserId();
    if (!userId) {
      if (typeof showNotification === 'function') {
        showNotification('Please sign in to edit queues', 'warning');
      }
      return;
    }

    // Load the queue
    const personalQueue = await window.personalQueueRepo.load(queueId);
    if (!personalQueue) {
      throw new Error('Queue not found');
    }

    // Set current editing state
    currentEditingQueue = personalQueue;
    editingQueueVideos = [...personalQueue.queueData.queue];
    
    // Update modal content
    updateEditModalContent();
    
    // Show modal
    showEditModal();
    
    console.log('📝 Edit modal opened for queue:', personalQueue.getTitle());
    
  } catch (error) {
    console.error('❌ Error opening edit modal:', error);
    if (typeof showNotification === 'function') {
      showNotification('Failed to open queue for editing', 'error');
    }
  }
}

/**
 * Create edit modal HTML structure
 */
function createEditModal() {
  // Check if modal already exists
  if (document.getElementById('queue-edit-modal')) {
    return;
  }

  const modalHTML = `
    <div id="queue-edit-modal" class="modal-overlay" style="display: none;">
      <div class="modal-container queue-edit-modal">
        <div class="modal-header">
          <h2 id="edit-modal-title">Edit Queue</h2>
          <button class="modal-close-btn" onclick="closeEditModal()">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>
        
        <div class="modal-body">
          <!-- Queue Info Section -->
          <div class="edit-section queue-info-section">
            <h3>Queue Information</h3>
            <div class="queue-info-form">
              <label for="edit-queue-title">Queue Title:</label>
              <input type="text" id="edit-queue-title" class="form-input" placeholder="Enter queue title">
            </div>
          </div>

          <!-- Add Videos Section -->
          <div class="edit-section add-videos-section">
            <h3>Add Videos</h3>
            <div class="search-container">
              <input type="text" id="edit-search-input" class="form-input" placeholder="Search for videos to add...">
              <button id="edit-search-btn" class="btn btn-primary">Search</button>
            </div>
            <div id="edit-search-results" class="search-results-container"></div>
          </div>

          <!-- Current Videos Section -->
          <div class="edit-section current-videos-section">
            <h3>Current Videos (<span id="edit-video-count">0</span>)</h3>
            <div class="queue-controls">
              <button id="edit-select-all-btn" class="btn btn-secondary">Select All</button>
              <button id="edit-remove-selected-btn" class="btn btn-danger" disabled>Remove Selected</button>
              <button id="edit-clear-queue-btn" class="btn btn-danger">Clear All</button>
            </div>
            <div id="edit-videos-list" class="edit-videos-container"></div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="closeEditModal()">Cancel</button>
          <button id="save-queue-changes-btn" class="btn btn-primary">Save Changes</button>
        </div>
      </div>
    </div>
  `;

  // Add modal to document
  document.body.insertAdjacentHTML('beforeend', modalHTML);
}

/**
 * Set up event listeners for edit modal
 */
function setupEditModalEventListeners() {
  // Search functionality
  const searchInput = document.getElementById('edit-search-input');
  const searchBtn = document.getElementById('edit-search-btn');
  
  if (searchInput && searchBtn) {
    searchInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        performEditSearch();
      }
    });
    
    searchBtn.addEventListener('click', performEditSearch);
  }

  // Queue controls
  const selectAllBtn = document.getElementById('edit-select-all-btn');
  const removeSelectedBtn = document.getElementById('edit-remove-selected-btn');
  const clearQueueBtn = document.getElementById('edit-clear-queue-btn');
  const saveChangesBtn = document.getElementById('save-queue-changes-btn');

  if (selectAllBtn) {
    selectAllBtn.addEventListener('click', selectAllVideos);
  }

  if (removeSelectedBtn) {
    removeSelectedBtn.addEventListener('click', removeSelectedVideos);
  }

  if (clearQueueBtn) {
    clearQueueBtn.addEventListener('click', clearEditingQueue);
  }

  if (saveChangesBtn) {
    saveChangesBtn.addEventListener('click', saveQueueChanges);
  }

  // Modal overlay click to close
  const modal = document.getElementById('queue-edit-modal');
  if (modal) {
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeEditModal();
      }
    });
  }
}

/**
 * Show edit modal
 */
function showEditModal() {
  const modal = document.getElementById('queue-edit-modal');
  if (modal) {
    modal.style.display = 'flex';
    isEditModalOpen = true;
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
  }
}

/**
 * Close edit modal
 */
function closeEditModal() {
  const modal = document.getElementById('queue-edit-modal');
  if (modal) {
    modal.style.display = 'none';
    isEditModalOpen = false;
    document.body.style.overflow = ''; // Restore scrolling
    
    // Clear editing state
    currentEditingQueue = null;
    editingQueueVideos = [];
    
    // Clear search results
    const searchResults = document.getElementById('edit-search-results');
    if (searchResults) {
      searchResults.innerHTML = '';
    }
    
    console.log('❌ Edit modal closed');
  }
}

/**
 * Update edit modal content with current queue data
 */
function updateEditModalContent() {
  if (!currentEditingQueue) return;

  // Update title
  const modalTitle = document.getElementById('edit-modal-title');
  const titleInput = document.getElementById('edit-queue-title');
  
  if (modalTitle) {
    modalTitle.textContent = `Edit: ${currentEditingQueue.getTitle()}`;
  }
  
  if (titleInput) {
    titleInput.value = currentEditingQueue.getTitle();
  }

  // Update videos list
  updateEditVideosList();
}

/**
 * Update the videos list in edit modal
 */
function updateEditVideosList() {
  const container = document.getElementById('edit-videos-list');
  const countElement = document.getElementById('edit-video-count');
  
  if (!container) return;

  // Update count
  if (countElement) {
    countElement.textContent = editingQueueVideos.length;
  }

  if (editingQueueVideos.length === 0) {
    container.innerHTML = `
      <div class="empty-state">
        <p>No videos in this queue</p>
        <p class="text-muted">Search and add videos above</p>
      </div>
    `;
    return;
  }

  // Generate videos HTML
  const videosHTML = editingQueueVideos.map((video, index) => createEditVideoItemHTML(video, index)).join('');
  container.innerHTML = videosHTML;

  // Add event listeners to new elements
  addEditVideoEventListeners();
}

/**
 * Create HTML for edit video item
 * @param {Object} video - Video object
 * @param {number} index - Video index
 * @returns {string} HTML string
 */
function createEditVideoItemHTML(video, index) {
  return `
    <div class="edit-video-item" data-index="${index}" data-video-id="${video.id}">
      <div class="video-checkbox">
        <input type="checkbox" class="video-select-checkbox" data-index="${index}">
      </div>
      <div class="video-thumbnail">
        <img src="${video.thumbnail}" alt="${escapeHtml(video.title)}" loading="lazy">
        <div class="video-duration">${video.duration || ''}</div>
      </div>
      <div class="video-info">
        <h4 class="video-title" title="${escapeHtml(video.title)}">${escapeHtml(video.title)}</h4>
        <p class="video-channel">${escapeHtml(video.channel || 'Unknown Channel')}</p>
        <div class="video-meta">
          <span class="video-views">${video.views || ''}</span>
          <span class="video-published">${video.publishedAt || ''}</span>
        </div>
      </div>
      <div class="video-controls">
        <button class="btn-icon move-up-btn" onclick="moveVideoUp(${index})" title="Move up" ${index === 0 ? 'disabled' : ''}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"/>
          </svg>
        </button>
        <button class="btn-icon move-down-btn" onclick="moveVideoDown(${index})" title="Move down" ${index === editingQueueVideos.length - 1 ? 'disabled' : ''}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6z"/>
          </svg>
        </button>
        <button class="btn-icon remove-video-btn" onclick="removeVideoFromEdit(${index})" title="Remove video">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
          </svg>
        </button>
      </div>
    </div>
  `;
}

/**
 * Add event listeners to edit video items
 */
function addEditVideoEventListeners() {
  // Checkbox change events
  const checkboxes = document.querySelectorAll('.video-select-checkbox');
  checkboxes.forEach(checkbox => {
    checkbox.addEventListener('change', updateRemoveSelectedButton);
  });
}

/**
 * Perform search for videos to add
 */
async function performEditSearch() {
  const searchInput = document.getElementById('edit-search-input');
  const resultsContainer = document.getElementById('edit-search-results');

  if (!searchInput || !resultsContainer) return;

  const query = searchInput.value.trim();
  if (!query) return;

  console.log('🔍 Searching for videos:', query);

  // Show loading state
  resultsContainer.innerHTML = `
    <div class="search-loading">
      <div class="loading-spinner"></div>
      <p>Searching for videos...</p>
    </div>
  `;

  try {
    // Use existing search functionality
    if (typeof searchYouTube === 'function') {
      const results = await searchYouTube(query);
      displayEditSearchResults(results);
    } else {
      throw new Error('Search function not available');
    }
  } catch (error) {
    console.error('❌ Search error:', error);
    resultsContainer.innerHTML = `
      <div class="search-error">
        <p>Failed to search for videos</p>
        <button onclick="performEditSearch()" class="btn btn-secondary">Try Again</button>
      </div>
    `;
  }
}

/**
 * Display search results in edit modal
 * @param {Array} results - Search results
 */
function displayEditSearchResults(results) {
  const container = document.getElementById('edit-search-results');
  if (!container) return;

  if (!results || results.length === 0) {
    container.innerHTML = `
      <div class="search-empty">
        <p>No videos found</p>
        <p class="text-muted">Try a different search term</p>
      </div>
    `;
    return;
  }

  const resultsHTML = results.map(video => createEditSearchResultHTML(video)).join('');
  container.innerHTML = `
    <div class="search-results-header">
      <h4>Search Results (${results.length})</h4>
    </div>
    <div class="search-results-list">
      ${resultsHTML}
    </div>
  `;
}

/**
 * Create HTML for search result item
 * @param {Object} video - Video object
 * @returns {string} HTML string
 */
function createEditSearchResultHTML(video) {
  const isAlreadyInQueue = editingQueueVideos.some(v => v.id === video.id);

  return `
    <div class="search-result-item ${isAlreadyInQueue ? 'already-added' : ''}" data-video-id="${video.id}">
      <div class="video-thumbnail">
        <img src="${video.thumbnail}" alt="${escapeHtml(video.title)}" loading="lazy">
        <div class="video-duration">${video.duration || ''}</div>
      </div>
      <div class="video-info">
        <h4 class="video-title" title="${escapeHtml(video.title)}">${escapeHtml(video.title)}</h4>
        <p class="video-channel">${escapeHtml(video.channel || 'Unknown Channel')}</p>
        <div class="video-meta">
          <span class="video-views">${video.views || ''}</span>
          <span class="video-published">${video.publishedAt || ''}</span>
        </div>
      </div>
      <div class="video-actions">
        ${isAlreadyInQueue ?
          '<span class="already-added-label">Already in queue</span>' :
          `<button class="btn btn-primary add-to-edit-btn" onclick="addVideoToEdit('${video.id}')">Add to Queue</button>`
        }
      </div>
    </div>
  `;
}

/**
 * Add video to editing queue
 * @param {string} videoId - Video ID to add
 */
async function addVideoToEdit(videoId) {
  try {
    // Find video in search results
    const searchResults = document.querySelectorAll('.search-result-item');
    let videoData = null;

    for (const item of searchResults) {
      if (item.dataset.videoId === videoId) {
        // Extract video data from DOM
        const thumbnail = item.querySelector('.video-thumbnail img')?.src || '';
        const title = item.querySelector('.video-title')?.textContent || '';
        const channel = item.querySelector('.video-channel')?.textContent || '';
        const duration = item.querySelector('.video-duration')?.textContent || '';
        const views = item.querySelector('.video-views')?.textContent || '';
        const publishedAt = item.querySelector('.video-published')?.textContent || '';

        videoData = {
          id: videoId,
          title,
          channel,
          thumbnail,
          duration,
          views,
          publishedAt
        };
        break;
      }
    }

    if (!videoData) {
      throw new Error('Video data not found');
    }

    // Check if already in queue
    if (editingQueueVideos.some(v => v.id === videoId)) {
      if (typeof showNotification === 'function') {
        showNotification('Video already in queue', 'warning');
      }
      return;
    }

    // Add to editing queue
    editingQueueVideos.push(videoData);

    // Update display
    updateEditVideosList();

    // Update search results to show as added
    displayEditSearchResults(getCurrentSearchResults());

    console.log('➕ Added video to edit queue:', title);

    if (typeof showNotification === 'function') {
      showNotification(`Added "${title}" to queue`, 'success');
    }

  } catch (error) {
    console.error('❌ Error adding video to edit queue:', error);
    if (typeof showNotification === 'function') {
      showNotification('Failed to add video', 'error');
    }
  }
}

/**
 * Remove video from editing queue
 * @param {number} index - Index of video to remove
 */
function removeVideoFromEdit(index) {
  if (index < 0 || index >= editingQueueVideos.length) return;

  const video = editingQueueVideos[index];
  editingQueueVideos.splice(index, 1);

  // Update display
  updateEditVideosList();

  // Update search results if they exist
  const searchResults = getCurrentSearchResults();
  if (searchResults && searchResults.length > 0) {
    displayEditSearchResults(searchResults);
  }

  console.log('➖ Removed video from edit queue:', video.title);

  if (typeof showNotification === 'function') {
    showNotification(`Removed "${video.title}" from queue`, 'info');
  }
}

/**
 * Move video up in queue
 * @param {number} index - Current index of video
 */
function moveVideoUp(index) {
  if (index <= 0 || index >= editingQueueVideos.length) return;

  // Swap with previous video
  [editingQueueVideos[index - 1], editingQueueVideos[index]] =
  [editingQueueVideos[index], editingQueueVideos[index - 1]];

  // Update display
  updateEditVideosList();

  console.log('⬆️ Moved video up:', editingQueueVideos[index - 1].title);
}

/**
 * Move video down in queue
 * @param {number} index - Current index of video
 */
function moveVideoDown(index) {
  if (index < 0 || index >= editingQueueVideos.length - 1) return;

  // Swap with next video
  [editingQueueVideos[index], editingQueueVideos[index + 1]] =
  [editingQueueVideos[index + 1], editingQueueVideos[index]];

  // Update display
  updateEditVideosList();

  console.log('⬇️ Moved video down:', editingQueueVideos[index + 1].title);
}

/**
 * Select all videos
 */
function selectAllVideos() {
  const checkboxes = document.querySelectorAll('.video-select-checkbox');
  checkboxes.forEach(checkbox => {
    checkbox.checked = true;
  });
  updateRemoveSelectedButton();
}

/**
 * Remove selected videos
 */
function removeSelectedVideos() {
  const checkboxes = document.querySelectorAll('.video-select-checkbox:checked');
  const indicesToRemove = Array.from(checkboxes).map(cb => parseInt(cb.dataset.index)).sort((a, b) => b - a);

  if (indicesToRemove.length === 0) return;

  // Confirm removal
  const count = indicesToRemove.length;
  if (!confirm(`Remove ${count} selected video${count === 1 ? '' : 's'} from queue?`)) {
    return;
  }

  // Remove videos (in reverse order to maintain indices)
  indicesToRemove.forEach(index => {
    editingQueueVideos.splice(index, 1);
  });

  // Update display
  updateEditVideosList();

  console.log(`🗑️ Removed ${count} selected videos`);

  if (typeof showNotification === 'function') {
    showNotification(`Removed ${count} video${count === 1 ? '' : 's'}`, 'info');
  }
}

/**
 * Clear editing queue
 */
function clearEditingQueue() {
  if (editingQueueVideos.length === 0) return;

  if (!confirm('Remove all videos from queue?')) {
    return;
  }

  editingQueueVideos = [];
  updateEditVideosList();

  console.log('🗑️ Cleared editing queue');

  if (typeof showNotification === 'function') {
    showNotification('Queue cleared', 'info');
  }
}

/**
 * Save queue changes
 */
async function saveQueueChanges() {
  try {
    if (!currentEditingQueue) {
      throw new Error('No queue being edited');
    }

    const titleInput = document.getElementById('edit-queue-title');
    const newTitle = titleInput?.value?.trim();

    if (!newTitle) {
      if (typeof showNotification === 'function') {
        showNotification('Please enter a queue title', 'warning');
      }
      return;
    }

    console.log('💾 Saving queue changes...');

    // Show loading state
    const saveBtn = document.getElementById('save-queue-changes-btn');
    if (saveBtn) {
      saveBtn.disabled = true;
      saveBtn.textContent = 'Saving...';
    }

    // Update queue data
    currentEditingQueue.queueData.title = newTitle;
    currentEditingQueue.queueData.queue = [...editingQueueVideos];
    currentEditingQueue.updateLastModified();

    // Save to Firebase
    await window.personalQueueRepo.save(currentEditingQueue);

    // Invalidate cache
    if (typeof invalidatePersonalQueuesCache === 'function') {
      invalidatePersonalQueuesCache();
    }

    // Update queue card in place
    if (typeof updateQueueCard === 'function') {
      updateQueueCard(currentEditingQueue.id, {
        title: newTitle,
        videoCount: editingQueueVideos.length,
        lastModified: new Date()
      });
    }

    console.log('✅ Queue changes saved successfully');

    if (typeof showNotification === 'function') {
      showNotification('Queue updated successfully', 'success');
    }

    // Close modal
    closeEditModal();

    // Refresh personal queues list
    if (typeof loadPersonalQueues === 'function') {
      loadPersonalQueues(true);
    }

  } catch (error) {
    console.error('❌ Error saving queue changes:', error);

    if (typeof showNotification === 'function') {
      showNotification('Failed to save changes', 'error');
    }
  } finally {
    // Restore save button
    const saveBtn = document.getElementById('save-queue-changes-btn');
    if (saveBtn) {
      saveBtn.disabled = false;
      saveBtn.textContent = 'Save Changes';
    }
  }
}

/**
 * Update remove selected button state
 */
function updateRemoveSelectedButton() {
  const selectedCheckboxes = document.querySelectorAll('.video-select-checkbox:checked');
  const removeBtn = document.getElementById('edit-remove-selected-btn');

  if (removeBtn) {
    removeBtn.disabled = selectedCheckboxes.length === 0;
    removeBtn.textContent = selectedCheckboxes.length > 0 ?
      `Remove Selected (${selectedCheckboxes.length})` :
      'Remove Selected';
  }
}

/**
 * Get current search results (for updating display)
 */
function getCurrentSearchResults() {
  const searchItems = document.querySelectorAll('.search-result-item');
  return Array.from(searchItems).map(item => {
    const videoId = item.dataset.videoId;
    const thumbnail = item.querySelector('.video-thumbnail img')?.src || '';
    const title = item.querySelector('.video-title')?.textContent || '';
    const channel = item.querySelector('.video-channel')?.textContent || '';
    const duration = item.querySelector('.video-duration')?.textContent || '';
    const views = item.querySelector('.video-views')?.textContent || '';
    const publishedAt = item.querySelector('.video-published')?.textContent || '';

    return {
      id: videoId,
      title,
      channel,
      thumbnail,
      duration,
      views,
      publishedAt
    };
  });
}

/**
 * Escape HTML to prevent XSS
 * @param {string} text - Text to escape
 * @returns {string} Escaped text
 */
function escapeHtml(text) {
  if (!text) return '';
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

// Export functions for global access
window.initializeQueueEditing = initializeQueueEditing;
window.editPersonalQueue = editPersonalQueue;
window.closeEditModal = closeEditModal;
window.addVideoToEdit = addVideoToEdit;
window.removeVideoFromEdit = removeVideoFromEdit;
window.moveVideoUp = moveVideoUp;
window.moveVideoDown = moveVideoDown;
window.saveQueueChanges = saveQueueChanges;

console.log('✅ Personal Queues Editing component loaded');
