/* YouTube Looper - Queue Editing Styles */

/* Edit Modal */
.queue-edit-modal {
  max-width: 900px;
  width: 90vw;
  max-height: 90vh;
  background: linear-gradient(135deg, #0f172a, #1e293b);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.queue-edit-modal .modal-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.queue-edit-modal .modal-header h2 {
  margin: 0;
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
}

.queue-edit-modal .modal-body {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  max-height: calc(90vh - 140px);
}

.queue-edit-modal .modal-footer {
  padding: 1.5rem 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

/* Edit Sections */
.edit-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
}

.edit-section h3 {
  margin: 0 0 1rem 0;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
}

.edit-section:last-child {
  margin-bottom: 0;
}

/* Queue Info Form */
.queue-info-form {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.queue-info-form label {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  margin-bottom: 0.25rem;
}

/* Search Container */
.search-container {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.search-container .form-input {
  flex: 1;
}

/* Search Results */
.search-results-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.2);
}

.search-results-header {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.search-results-header h4 {
  margin: 0;
  color: white;
  font-size: 1rem;
  font-weight: 500;
}

.search-results-list {
  padding: 0.5rem;
}

.search-result-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  margin-bottom: 0.5rem;
}

.search-result-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.search-result-item.already-added {
  opacity: 0.6;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.search-result-item:last-child {
  margin-bottom: 0;
}

/* Queue Controls */
.queue-controls {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

/* Edit Videos Container */
.edit-videos-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.2);
  padding: 0.5rem;
}

.edit-video-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  margin-bottom: 0.5rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.edit-video-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.edit-video-item:last-child {
  margin-bottom: 0;
}

/* Video Components */
.video-checkbox {
  flex-shrink: 0;
}

.video-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #10b981;
}

.video-thumbnail {
  position: relative;
  flex-shrink: 0;
  width: 80px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-duration {
  position: absolute;
  bottom: 2px;
  right: 2px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 0.75rem;
  padding: 2px 4px;
  border-radius: 3px;
}

.video-info {
  flex: 1;
  min-width: 0;
}

.video-title {
  margin: 0 0 0.25rem 0;
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-channel {
  margin: 0 0 0.25rem 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-meta {
  display: flex;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

.video-meta span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Video Controls */
.video-controls {
  display: flex;
  gap: 0.25rem;
  flex-shrink: 0;
}

.video-actions {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.already-added-label {
  color: #10b981;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Button Icons */
.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-icon:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.btn-icon:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.remove-video-btn:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

/* Empty States */
.empty-state,
.search-loading,
.search-error,
.search-empty {
  text-align: center;
  padding: 2rem;
  color: rgba(255, 255, 255, 0.6);
}

.search-loading .loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-top: 2px solid #10b981;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .queue-edit-modal {
    width: 95vw;
    max-height: 95vh;
  }
  
  .queue-edit-modal .modal-body {
    padding: 1rem;
  }
  
  .edit-section {
    padding: 1rem;
  }
  
  .search-container {
    flex-direction: column;
  }
  
  .queue-controls {
    flex-direction: column;
  }
  
  .edit-video-item,
  .search-result-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .video-info {
    width: 100%;
  }
  
  .video-controls,
  .video-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
